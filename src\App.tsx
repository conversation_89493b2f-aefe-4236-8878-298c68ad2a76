// src/App.tsx
import React, { useState, useEffect, useRef } from 'react';
import StandbyDisplay from './components/StandbyDisplay';
import DanmuDisplay from './components/DanmuDisplay';
import CameraPreview from './components/CameraPreview';
import useHumanDetection from './hooks/useHumanDetection';
import useImageAnonymization from './hooks/useImageAnonymization';
import useVisionAnalysis from './hooks/useVisionAnalysis';
import usePraiseGenerator from './hooks/usePraiseGenerator';

function App() {
  const [isStandby, setIsStandby] = useState(true);
  const [praises, setPraises] = useState<string[]>([]);
  const [showCameraPreview, setShowCameraPreview] = useState(true); // 默认显示摄像头预览
  const [isHumanDetected, setIsHumanDetected] = useState(false); // 跟踪是否检测到人体
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const captureCountRef = useRef(0); // 跟踪捕获次数
  
  const { anonymizeImage } = useImageAnonymization();
  const { analyzeImage } = useVisionAnalysis();
  const { generatePraise } = usePraiseGenerator();
  
  // 获取摄像头访问权限
  const getCameraAccess = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { width: 640, height: 480 } 
      });
      streamRef.current = stream;
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
      return stream;
    } catch (error) {
      console.error('无法访问摄像头:', error);
      return null;
    }
  };
  
  // 捕获当前视频帧并转换为base64
  const captureImage = (): Promise<string> => {
    return new Promise((resolve) => {
      if (!videoRef.current) {
        resolve("");
        return;
      }
      
      const video = videoRef.current;
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const dataUrl = canvas.toDataURL('image/jpeg', 0.8);
        const base64Data = dataUrl.split(',')[1]; // 移除"data:image/jpeg;base64,"前缀
        resolve(base64Data);
      } else {
        resolve("");
      }
    });
  };
  
  const handleHumanDetected = async () => {
    console.log("handleHumanDetected 被调用");
    setIsHumanDetected(true); // 标记检测到人体
    
    // 只有在待机状态下才切换到弹幕显示模式
    if (isStandby) {
      setIsStandby(false);
      setPraises([]); // 清空之前的弹幕
      captureCountRef.current = 0; // 重置捕获计数
    }
    
    // 确保摄像头已启用
    if (!streamRef.current) {
      await getCameraAccess();
    }
    
    // 每次检测到人体时增加捕获计数
    captureCountRef.current++;
    
    // 每第3次检测执行图像捕获和分析（控制频率）
    if (captureCountRef.current % 3 === 1) {
      // 等待一小段时间确保画面稳定
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 捕获图像
      const imageData = await captureImage();
      console.log("图像捕获完成，大小:", imageData.length);
      
      // Step 1: Anonymize image
      const anonymizedImage = await anonymizeImage(imageData);
      
      // Step 2: Analyze image
      const visionResult = await analyzeImage(anonymizedImage);
      
      // Step 3: Generate praises
      const newPraises = generatePraise(visionResult);
      console.log("生成的赞美:", newPraises);
      
      // 添加新赞美到现有弹幕列表
      setPraises(prev => [...prev, ...newPraises]);
    }
  };
  
  // 处理人体未检测到的情况
  const handleHumanNotDetected = () => {
    console.log("未检测到人体");
    setIsHumanDetected(false);
    
    // 只有在非待机状态且未检测到人体时才切换回待机模式
    if (!isStandby) {
      setIsStandby(true);
      setPraises([]);
      captureCountRef.current = 0; // 重置捕获计数
    }
  };
  
  // 初始化摄像头访问
  useEffect(() => {
    getCameraAccess();
    
    return () => {
      // 清理摄像头流
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);
  
  // 使用人体检测hook，但添加人体未检测到的处理
  useHumanDetection(() => {
    handleHumanDetected();
    
    // 设置一个定时器来检查人体是否仍然存在
    const checkInterval = setInterval(async () => {
      try {
        const response = await fetch('http://127.0.0.1:5000/detection_status');
        const data = await response.json();
        
        if (!data.human_detected) {
          clearInterval(checkInterval);
          handleHumanNotDetected();
        }
      } catch (error) {
        console.error('检查人体状态失败:', error);
        clearInterval(checkInterval);
        handleHumanNotDetected();
      }
    }, 1000); // 每秒检查一次人体状态
  });

  return (
    <div className="w-screen h-screen overflow-hidden">
      {/* 隐藏的视频元素用于捕获图像 */}
      <video 
        ref={videoRef} 
        autoPlay 
        muted 
        playsInline
        className="hidden"
      />
      
      {isStandby ? (
        <>
          <StandbyDisplay />
          <CameraPreview isActive={showCameraPreview} />
        </>
      ) : (
        <>
          <DanmuDisplay praises={praises} />
          <CameraPreview isActive={showCameraPreview} />
        </>
      )}
    </div>
  );
}

export default App;