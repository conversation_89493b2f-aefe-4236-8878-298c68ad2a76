import cv2
import numpy as np
import base64
import json
from flask import Flask, request, jsonify
from flask_cors import CORS
import threading
import time

app = Flask(__name__)
CORS(app)

# 全局变量用于存储检测状态
human_detected = False
last_detection_time = 0
DETECTION_THRESHOLD = 1.5  # 1.5秒检测阈值

# 初始化摄像头，使用DirectShow后端
print("正在初始化摄像头...")
cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)  # 使用DirectShow后端

if not cap.isOpened():
    print("错误：无法打开摄像头")
else:
    print("摄像头初始化成功")
    # 设置摄像头参数
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)
    print(f"摄像头分辨率: {cap.get(cv2.CAP_PROP_FRAME_WIDTH)}x{cap.get(cv2.CAP_PROP_FRAME_HEIGHT)}")
    print(f"摄像头FPS: {cap.get(cv2.CAP_PROP_FPS)}")
    
    # 测试读取一帧
    print("测试读取摄像头帧...")
    for i in range(5):
        ret, frame = cap.read()
        if ret:
            print(f"成功读取测试帧，尺寸: {frame.shape}")
            break
        else:
            print(f"无法读取测试帧 {i+1}")
            time.sleep(0.5)
    if not ret:
        print("警告：摄像头可能无法正常工作")

# 加载Haar级联分类器进行人脸检测
print("加载人脸检测分类器...")
face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
print("人脸检测分类器加载完成")

# 人体检测函数
def detect_human():
    global human_detected, last_detection_time
    
    print("启动人体检测线程...")
    frame_count = 0
    while True:
        ret, frame = cap.read()
        frame_count += 1
        
        if not ret:
            if frame_count % 50 == 0:  # 每50次循环打印一次错误信息
                print("无法从摄像头读取帧")
            time.sleep(0.1)
            continue
            
        # 每100帧打印一次成功信息
        if frame_count % 100 == 0:
            print(f"成功读取帧 {frame_count}")
            
        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 人脸检测
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        if frame_count % 50 == 0:  # 每50帧打印一次检测结果
            print(f"检测到 {len(faces)} 个人脸")
        
        # 如果有人脸，则认为检测到人体
        if len(faces) > 0:
            if not human_detected:
                human_detected = True
                last_detection_time = time.time()
                print("检测到人体")
        else:
            human_detected = False
            
        time.sleep(0.1)  # 降低检测频率

# 图像匿名化函数
def anonymize_image(image_data):
    try:
        # 解码base64图像
        nparr = np.frombuffer(base64.b64decode(image_data), np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if img is None:
            raise Exception("无法解码图像数据")
        
        print(f"接收到的图像尺寸: {img.shape}")
        
        # 人脸检测
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        print(f"在接收到的图像中检测到 {len(faces)} 个人脸")
        
        # 对检测到的人脸进行模糊处理
        for (x, y, w, h) in faces:
            # 提取人脸区域
            face_region = img[y:y+h, x:x+w]
            
            # 应用马赛克效果
            # 缩小图像
            small_face = cv2.resize(face_region, (w//15, h//15))
            # 放大图像以创建马赛克效果
            mosaic_face = cv2.resize(small_face, (w, h), interpolation=cv2.INTER_NEAREST)
            
            # 将马赛克后的人脸放回原图
            img[y:y+h, x:x+w] = mosaic_face
        
        # 如果检测到人体，裁切画面到人体区域
        if len(faces) > 0:
            # 计算包含所有面部的边界框
            x_min = min([x for (x, y, w, h) in faces])
            y_min = min([y for (x, y, w, h) in faces])
            x_max = max([x + w for (x, y, w, h) in faces])
            y_max = max([y + h for (x, y, w, h) in faces])
            
            # 添加边距
            margin_x = int((x_max - x_min) * 0.2)
            margin_y = int((y_max - y_min) * 0.2)
            
            x_min = max(0, x_min - margin_x)
            y_min = max(0, y_min - margin_y)
            x_max = min(img.shape[1], x_max + margin_x)
            y_max = min(img.shape[0], y_max + margin_y)
            
            # 裁切图像
            img = img[y_min:y_max, x_min:x_max]
            print(f"裁切后图像尺寸: {img.shape}")
        
        # 编码回base64
        _, buffer = cv2.imencode('.jpg', img, [int(cv2.IMWRITE_JPEG_QUALITY), 85])
        anonymized_image = base64.b64encode(buffer).decode('utf-8')
        
        return anonymized_image
    except Exception as e:
        print(f"图像匿名化过程中出错: {str(e)}")
        raise e

# API路由
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy'})

@app.route('/detection_status', methods=['GET'])
def get_detection_status():
    global human_detected, last_detection_time
    
    print(f"检测状态查询 - human_detected: {human_detected}, last_detection_time: {last_detection_time}")
    # 检查是否持续检测到人体超过阈值
    if human_detected and (time.time() - last_detection_time) >= DETECTION_THRESHOLD:
        print("返回检测状态: True")
        return jsonify({'human_detected': True})
    else:
        print("返回检测状态: False")
        return jsonify({'human_detected': False})

@app.route('/anonymize', methods=['POST'])
def anonymize():
    try:
        data = request.json
        image_data = data.get('image_data', '')
        
        if not image_data:
            return jsonify({'error': 'No image data provided'}), 400
        
        print(f"接收到图像数据，大小: {len(image_data)} 字符")
        
        anonymized_image = anonymize_image(image_data)
        print("图像匿名化完成")
        
        return jsonify({'anonymized_image': anonymized_image})
    except Exception as e:
        print(f"匿名化API出错: {str(e)}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # 启动人体检测线程
    detection_thread = threading.Thread(target=detect_human, daemon=True)
    detection_thread.start()
    
    print("启动OpenCV服务...")
    app.run(host='127.0.0.1', port=5000, debug=False)