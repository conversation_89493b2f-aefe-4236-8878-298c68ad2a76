import React, { useState, useEffect, useRef } from 'react';

interface CameraPreviewProps {
  isActive: boolean;
}

const CameraPreview: React.FC<CameraPreviewProps> = ({ isActive }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const [error, setError] = useState<string | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 获取摄像头权限并显示预览
  useEffect(() => {
    if (!isActive) return;

    const getCameraStream = async () => {
      try {
        // 先停止任何现有的流
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }
        
        // 清除之前的错误信息
        setError(null);
        
        const stream = await navigator.mediaDevices.getUserMedia({ 
          video: { 
            width: { ideal: 640 },
            height: { ideal: 480 },
            facingMode: 'user'
          } 
        });
        
        streamRef.current = stream;
        
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
      } catch (err) {
        console.error('无法访问摄像头:', err);
        const errorMessage = (err as Error).message || '未知错误';
        setError('无法访问摄像头: ' + errorMessage);
        
        // 如果是设备繁忙错误，尝试重新连接
        if (errorMessage.includes('Could not start video source') || 
            errorMessage.includes('TrackStart') ||
            errorMessage.includes('NotReadableError')) {
          console.log('摄像头可能被占用，将在2秒后重试...');
          if (retryTimeoutRef.current) {
            clearTimeout(retryTimeoutRef.current);
          }
          retryTimeoutRef.current = setTimeout(() => {
            if (isActive) {
              getCameraStream();
            }
          }, 2000);
        }
      }
    };

    getCameraStream();

    // 清理函数
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }
    };
  }, [isActive]);

  if (!isActive) {
    return null;
  }

  return (
    <div className="absolute top-4 right-4 w-80 h-60 bg-black rounded-lg overflow-hidden border-2 border-white">
      {error ? (
        <div className="w-full h-full flex flex-col items-center justify-center text-red-500 text-sm p-2 text-center">
          <div>{error}</div>
          <div className="mt-2 text-xs text-gray-400">请确保摄像头未被其他应用占用</div>
        </div>
      ) : (
        <>
          <video 
            ref={videoRef} 
            autoPlay 
            muted 
            playsInline
            className="w-full h-full object-cover"
          />
          <canvas 
            ref={canvasRef} 
            width={320} 
            height={240} 
            className="absolute top-0 left-0 w-full h-full pointer-events-none"
          />
        </>
      )}
      <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
        摄像头预览
      </div>
    </div>
  );
};

export default CameraPreview;